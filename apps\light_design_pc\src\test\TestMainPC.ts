import { Vector2 } from "three";

import { DomainApiHub } from "@layoutai/design_domain";

import { LayoutSchemeService } from "../service/LayoutSchemeService";


/**
* @description 测试主类
* <AUTHOR>
* @date 2025-06-19
* @lastEditTime 2025-06-19 14:13:30
* @lastEditors xuld
*/
export class TestMainPC {
    private static _isTest: boolean = false;

    public static async main() {
        if (this._isTest) {
            return;
        }
        this._isTest = true;

        // this.testWallRoom();
        await this.testScheme();
    }

    private static testWallRoom() {
        let points: [Vector2, Vector2][] = [
            [new Vector2(2880, -5860), new Vector2(-7360, -5860)],
            [new Vector2(3000, 4300), new Vector2(3000, -5980)],
            [new Vector2(-7120, 4420), new Vector2(3120, 4420)],
            [new Vector2(-7240, -5740), new Vector2(-7240, 4540)],
        ];

        for (let i = 0; i < points.length; i++) {
            let start: Vector2 = points[i][0];
            let end: Vector2 = points[i][1];
            let uuid = DomainApiHub.instance.createWall(start, end, 240, 2800);
            console.log(i, uuid);
        }
    }

    private static async testScheme() {
        let layoutSchemeId = '38a762b4ab1b408f8f86aac68987452a';
        let schemeData = await LayoutSchemeService.getLayoutSchemeById(layoutSchemeId);
        if (schemeData) {
            let res = await DomainApiHub.instance.importFromSchemeData(schemeData);
            console.log("xxxxx", res);
        } else {
            console.log("schemeData is null", layoutSchemeId);
        }
    }

}