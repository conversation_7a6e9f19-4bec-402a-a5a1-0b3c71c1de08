import { Vector3 } from "three";
import { getPrefix } from "@layoutai/basic_data";
import { I_RoomModelLocs } from "@layoutai/basic_data";
import { TSize, TSizeRange } from "./TSizeRange";


/**
 *   系列: 单个图元尺寸链数据库
 */
export class TSerialSizeRangeDB {
    static _instance: TSerialSizeRangeDB | null = null;
    private _dict: { [key: string]: { [key: string]: { size_range: TSizeRange, modelIds?: string[] }[] } };

    private last_serial_id: string;
    constructor() {
        this._dict = { "default": {} };
        this.last_serial_id = "default";
    }

    static getInstance() {
        if (!TSerialSizeRangeDB._instance) {
            TSerialSizeRangeDB._instance = new TSerialSizeRangeDB();
        }
        return TSerialSizeRangeDB._instance;
    }
    static async LoadSeriesFromAiDesk(serial_id: string = "default") {
        let dict = TSerialSizeRangeDB.getInstance()._dict;

        if (!dict[serial_id]) dict[serial_id] = {};

        // let model_locs : {rooms : I_RoomModelLocs[]} = await quick_axios.get("https://3vj-pano.oss-cn-shenzhen.aliyuncs.com/vr/config/aidesk/aideskmodelloc.json",{}).then(res=>res.data).catch(e=>null);
        // if(!model_locs)
        // {
        //     model_locs = await fetch("./static/db/SizeRangeDB/aideskmodelloc.json").then(val=>val.json());
        // }
        let model_locs: { rooms: I_RoomModelLocs[] } = await fetch(getPrefix() + "./static/db/SizeRangeDB/aideskmodelloc.json").then(val => val.json());

        if (!model_locs || !model_locs?.rooms) return;

        for (let room_loc of model_locs.rooms) {
            if (!room_loc?.modelloc) continue;
            for (let m_loc of room_loc.modelloc) {
                let key = m_loc.name;
                dict[serial_id][key] = [];

                if (!m_loc.chain) continue;

                for (let chain of m_loc.chain) {
                    if (chain.indexOf("非建议") >= 0) continue;

                    let len_strs = chain.split("~");
                    if (len_strs[0] && len_strs[1]) {
                        len_strs[0] = len_strs[0].replace("mm", "");
                        len_strs[1] = len_strs[1].replace("mm", "");

                        let x_min = ~~len_strs[0];
                        let x_max = ~~len_strs[1];

                        let size_range = new TSizeRange().fromJson({ min: { x: x_min - 1, y: 0, z: 0 }, max: { x: x_max, y: 0, z: 0 } });
                        let t_range_data = {
                            size_range: size_range,
                            modelIds: []
                        };
                        dict[serial_id][key].push(t_range_data);


                        // console.log(chain, x_min, x_max);
                    } else {
                        console.log(chain);
                    }
                }
            }
        }

    }
    static async LoadSeries(serial_id: string = "default") {
        let dict = TSerialSizeRangeDB.getInstance()._dict;

        if (serial_id === TSerialSizeRangeDB.getInstance().last_serial_id) {
            let data = await fetch(getPrefix() + "./static/db/SizeRangeDB/default.json").then(val => val.json());
            dict[serial_id] = {};

            for (let key in data) {
                let t_data = data[key];
                dict[serial_id][key] = [];

                for (let range_data of t_data) {
                    let t_range_data = {
                        size_range: new TSizeRange().fromJson(range_data.size_range),
                        modelIds: range_data.modelIds || []
                    };
                    dict[serial_id][key].push(t_range_data);
                }
            }
        }

        let t_dict = dict[serial_id];
        for (let key in t_dict) {
            t_dict[key].sort((a, b) => {
                if (Math.abs(b.size_range.max.x - a.size_range.max.x) < 1) {
                    return b.size_range.max.y - a.size_range.max.y;
                }
                else {
                    return b.size_range.max.x - a.size_range.max.x;
                }
            });
        }

    }
    static GetSizeRangeList(figure_name: string, serial_id: string = "default"): { size_range: TSizeRange, modelIds?: string[] }[] {
        let dict = TSerialSizeRangeDB.getInstance()._dict;
        serial_id = serial_id == "default" ? TSerialSizeRangeDB.getInstance().last_serial_id : serial_id;
        if (!dict[serial_id]) return [];
        return dict[serial_id][figure_name] || [];
    }

    static SetSizeRangeList(size_range_list: { size_range: TSizeRange, modelIds?: string[] }[], figure_name: string, serial_id: string = "default") {
        let dict = TSerialSizeRangeDB.getInstance()._dict;
        serial_id = serial_id == "default" ? TSerialSizeRangeDB.getInstance().last_serial_id : serial_id;
        if (!dict[serial_id]) {
            dict[serial_id] = {}
            return;
        }
        dict[serial_id][figure_name] = size_range_list;
    }

    static QueryDefaultModelIds(figure_name: string, size: TSize, serial_id: string = "default") {
        let list = TSerialSizeRangeDB.GetSizeRangeList(figure_name, serial_id);
        if (!list) return null;

        let modelIds: string[] = [];
        for (let range_data of list) {
            if (range_data.size_range.containsPoint(new Vector3(size.x, size.y, size.z))) {
                modelIds = range_data.modelIds ?? [];
            }
        }
        return modelIds;
    }
}