import { hxUrl } from '../config/host';
// import { isArray } from 'lodash';


export function getPrefix() {
  if ((globalThis as any).NODE_ENV === 'development') {
    return "";
  }
  return (globalThis as any).__PUBLIC_PATH__ || ''
}


export function onModal_HxSearch() {
  const iframe = document.createElement('iframe');
  iframe.src = hxUrl;
  iframe.id = 'searchIframe';
  iframe.setAttribute('frameborder', '0');
  iframe.setAttribute('scrolling', 'no');
  iframe.onload = function iframeLoaded() {
    setTimeout(() => {
      iframe.contentWindow?.postMessage({
        origin: 'uiapi.3dhouseplugin',
        data: {
          name: 'houseHome',
          action: 'open',
          params: {
            hasCollection: true,
            hasExcellentScheme: false,
            houseCardBtnArr: [],
            houseDetailBtnArr: [{ label: '开始设计', value: '1' }],
          }
        }
      }, '*')
    }, 1000)

  }
  const iframeWrap = document.getElementById('iframe-wrap');
  iframeWrap?.appendChild(iframe);


}